import {get, set} from 'lodash'
import moment from 'moment'
import * as uuid from 'uuid'

// Import salesforce controller
import { salesforceController as sf_controller, cloudTalkController, userRolesController } from '@controllers'

//Import utils functions
import {
  validate,
  renameFile,
  createUpdateUserProfile,
  buildUpdate,
  getObjectValues,
  insertLogChange,
  sendEmail,
  validateEmail as mailgunValidateEmail,
  uploadLogo,
} from '@utils'

// Import connectors
import {postgres, transactional_client} from '@connectors/postgres'
import { SalesforceMethods } from '@connectors/salesforce'
import * as auth0 from '@connectors/auth0-functions'

// Import queries
import { users_queries as queries, user_schools_queries, insertQuery } from '@queries'

// Import additional controllers
import { userStudentsController as user_students_controller } from './'

//Import object schemas
import * as schemas from '@schemas'

//Import interfaces
import {
  UpdateUser,
  NewUserD<PERSON>,
  CreateUserWithStudent,
  UserInfo,
  UserApplication,
  SObjectContact,
  SchoolSearchNoMatch,
  UpdateSchooluser,
  GetUsersByRoleIdResponse,
  UserLogAuth
} from '@interfaces'

// Import html generator
import schoolSearchNoMatch from '@email-templates/user-search-no-match-request-info'

// Import errors
import { badRequestError, notFoundError, serverError } from '@errors'

import { INFO_RECIPIENT } from '@constants'

import { generators } from '@utils'

/**
 * Users controller functions
 * Used by /src/routes/users.ts
 */

/**
 *
 * @param  {Array<string>} user_ids
 * @returns {Promise} formatted response object with the user_id as the key and the image url as the value
 */
export async function getUserAvatars(user_ids: Array<string>): Promise<unknown>{
  try {
    const users = await auth0.getUsersAvatars(user_ids)
    // Match format for the response
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const result = users.reduce((accumulator: any, user) => {
      accumulator[user.user_id] = get(user, 'user_metadata.picture') || user.picture
      return accumulator
    }, {})
    return result
  } catch (error) {
    console.error('getUserAvatarsError:', error)
    throw serverError
  }
}

/**
 *
 * @param  {string} email
 * @returns {Promise} formatted response object with the user id for the given email
 */
export async function getUserIdByEmail(email: string) {
  try {
    const user = await auth0.getUserByEmail(email)
    return {
      userId: user?.user_id || ''
    }
  } catch (error) {
    console.error('getUserIdByEmailError:', error)
    throw serverError
  }
}

/**
 *
 * @param {string} user_id
 * @returns {Promise} response object with the user information for the given user_id
 */
export async function getUserDetails(user_id: string) {
  try {
    const user = await auth0.getUser(user_id)
    return user
  } catch (error) {
    console.error('getUserDetailsError:', error)
    if(error.statusCode === notFoundError.statusCode) throw notFoundError
    throw serverError
  }
}

/**
 *
 * @param {string} user_id
 * @returns {Promise} returns an array of search objects from the database for a given user_id
 */
export async function getSearches(user_id: string): Promise<unknown>{
  try {
    // Try to get the user, if no user throws not found error
    await auth0.getUser(user_id)
    const db_response = await postgres.query(queries.getSearches(), [user_id])
    return db_response.rows
  } catch (error) {
    console.error('getSearchesError:', error)
    if(error.statusCode === notFoundError.statusCode) throw notFoundError
    throw serverError
  }
}

/**
 *
 * @param {string} user_id
 * @returns {Promise} returns an array of tour objects from the database for a given user_id
 */
export async function getTourList(user_id: string): Promise<unknown>{
  try {
    // Try to get the user, if no user throws not found error
    await auth0.getUser(user_id)
    const db_response = await postgres.query(queries.getTourList(), [user_id])
    return db_response.rows
  } catch (error) {
    console.error('getTourListError:', error)
    if(error.statusCode === notFoundError.statusCode) throw notFoundError
    throw serverError
  }
}

/**
 *
 * @param {string} user_id
 * @returns {Promise} returns an array of user application objects from the database for a given user_id
 */
export async function getUserApplications(user_id: string): Promise<unknown>{
  try {
    // Try to get the user, if no user throws not found error
    await auth0.getUser(user_id)
    const db_response = await postgres.query(queries.getUserApplications(), [user_id])
    return db_response.rows
  } catch (error) {
    console.error('getUserApplicationsError:', error)
    if(error.statusCode === notFoundError.statusCode) throw notFoundError
    throw serverError
  }
}

/**
 *
 * @param  {string} user_id
 * @param  {number} application_id
 * @returns {Promise} returns an array of user application objects from the database for a given user_id
 */
export async function getUserApplication(user_id: string, application_id: number): Promise<unknown>{
  try {
    // Try to get the user, if no user throws not found error
    await auth0.getUser(user_id)
    const db_response = await postgres.query(queries.getUserApplication(), [application_id])
    const user_application = db_response.rows[0]
    const student_id = Number(user_application.student_id)
    const student = await user_students_controller.getUserStudent({
      user_id,
      id: student_id
    })
    user_application.student = student
    return user_application
  } catch (error) {
    console.error('getUserApplicationError:', error)
    if(error.statusCode === notFoundError.statusCode) throw notFoundError
    throw serverError
  }
}

/**
 *
 * @param {string} user_id
 * @returns {Promise} returns an object representation of the user profile from the database
 */
export async function getUserProfile(user_id: string): Promise<unknown>{
  try {
    // Try to get the user, if no user throws not found error
    const user = await this.getUserDetails(user_id)
    if (!user) throw notFoundError
    let db_response = await postgres.query(queries.getUserProfile(), [user_id])
    const user_profile = db_response.rows[0] || {}
    // Get user schools by user_id
    db_response = await postgres.query(queries.getUserSchoolByUserId(), [user_id])
    const user_schools = db_response.rows
    // Validate partner_id
    if (!user_profile || !user_profile.partner_id) return {
      ...user_profile,
      userSchool: user_schools,
      user_metadata: user.user_metadata,
      last_login: user.last_login,
      email: user.email
    }
    // Get the partner users
    db_response = await postgres.query(queries.getPartnerUsers(), [user_profile.partner_id])
    return {
      ...user_profile,
      partner_users: db_response.rows,
      user_metadata: user.user_metadata,
      last_login: user.last_login,
      email: user.email
    }
  } catch (error) {
    console.error('getUserProfileError:', error)
    if(error.statusCode === notFoundError.statusCode) throw notFoundError
    throw serverError
  }
}

/**
 *
 * @param {string} user_id
 * @returns {Promise} returns an object with the response from the database
 */
export async function getScholaMatchCompleted(user_id: string): Promise<unknown>{
  try {
    const db_response = await postgres.query(queries.getScholaMatchCompleted(), [user_id])
    if(db_response.rowCount === 0) throw notFoundError
    const completed_scholamatch = db_response.rows[0]
    return {
      leads: Number(completed_scholamatch.leads),
      applications: Number(completed_scholamatch.applications),
      completed: Number(completed_scholamatch.leads) > 0
    }
  } catch (error) {
    console.error('getScholaMatchCompletedError:', error)
    if(error.statusCode === notFoundError.statusCode) throw notFoundError
    throw serverError
  }
}

/**
 *
 * @param  {string} token
 * @returns {Promise} returns an object with the response from the database
 */
export async function getNewToken(token: string): Promise<unknown>{
  try {
    const id_token = await auth0.getNewDelegationToken(token)
    return {
      idToken: id_token
    }
  } catch (error) {
    console.error('getNewTokenError:', error)
    throw serverError
  }
}

/**
 *
 * @param  {string} user_id
 * @param  {number} school_id
 * @returns {Promise} returns an object with the response from the database
 */
export async function getSchoolUserNotifications(user_id: string, school_id: number): Promise<{notify_new_lead: string, notify_new_app: string}> {
  try {
    const db_response = await postgres.query(queries.getSchoolUserNotifications(), [school_id, user_id])
    if(db_response.rowCount === 0) throw notFoundError
    const school_user_notifications = db_response.rows[0]
    return {
      notify_new_lead: school_user_notifications.notify_new_lead,
      notify_new_app: school_user_notifications.notify_new_app
    }
  } catch (error) {
    console.error('getSchoolUserNotificationsError:', error)
    if(error.statusCode === notFoundError.statusCode) throw notFoundError
    throw serverError
  }
}

/**
 *
 * @param {string} user_id
 * @returns {Promise} returns an object with the response from the database
 */
export async function getLeadsByUserId(user_id: string): Promise<Array<unknown>>{
  try {
    const db_response = await postgres.query(queries.getLeadsByUserId(), [user_id])
    return db_response.rows
  } catch (error) {
    console.error('getLeadsByUserIdError:', error)
    throw serverError
  }
}

/**
 *
 * @param  {UpdateUser} params
 * @param  {string} params.user_id
 * @param  {string} params.token
 * @param  {string} params.decoded_email
 * @param  {Buffer} [params.avatar]
 * @param  {string} [params.fileName]
 * @param  {Buffer} [params.original]
 * @param  {string} [params.password]
 * @param  {string} [params.email]
 * @returns {Promise} returns an object with the response from the database
 */
export async function updateUser(params: UpdateUser): Promise<unknown>{
  await validate(params, schemas.update_user)
  const {user_id, fileName: file_name, avatar, original} = params
  // Try to get the user, if no user throws not found error
  const user = await this.getUserDetails(user_id)
  if (!user) throw notFoundError
  try {
    const new_user_data: NewUserData = {}
    if (params.avatar) {
      const renamed_file = file_name ? renameFile(file_name) : `profile-${moment(Date.now()).format('YYYYMMDDHHmmss')}`
      const upload_logo_result = await uploadLogo( {
        original,
        subfolder: `user/${user_id}/profile_images`,
        filename: renamed_file,
        alt_img: avatar
      })
      new_user_data.user_metadata = {
        picture: upload_logo_result.relative_path
      }
    }
    if(params.email) {
      // Sign in to verify that the provided password is correct
      // Use email from token because params.email is not active yet.
      await auth0.signIn(params.decoded_email, params.password)
      new_user_data.email = params.email
    }
    // Update the auth0 user registry
    await auth0.updateUser(params.user_id, new_user_data)
    const new_token = await this.getNewToken(params.token)
    return new_token
  } catch (error) {
    console.error('updateUserError:', error)
    throw serverError
  }
}

/**
 *
 * @param  {string} user_id
 * @param  {string} username
 * @param  {string} password
 * @returns {Promise} returns an object with the response from the database
 */
export async function requestPasswordChange(user_id: string, password: string): Promise<unknown>{
  try {
    // Try to get the user, if no user throws not found error
    const user = await this.getUserDetails(user_id)
    if (!user) throw notFoundError
    await auth0.signIn(user.email, password)
    return await auth0.createPasswordChangeTicket(user_id)
  } catch (error) {
    console.error('requestPasswordChangeError:', error)
    if(error.statusCode === notFoundError.statusCode) throw notFoundError
    throw serverError
  }
}

/**
 * @param  {string} name
 * @param  {string} phone
 * @param  {string} email
 * @returns {Promise} returns an object with the response from the database
 */
export async function createUser(name: string, phone: string, email: string, spot_user?: boolean): Promise<unknown>{
  await validate({email}, schemas.validate_email)
  try {
    const temporary_password = uuid.v4() + 'aA1'
    const user = await auth0.createUser({
      email,
      password: temporary_password,
      phone,
      name,
      is_temp_password: true,
      spot_user
    })

    if (user && user.user_id) {
      await createUpdateUserProfile({ user_id: user.user_id, email, first_name: name.split(' ')[0] || '', last_name: name.split(' ')[1] || '' })
    }

    return user
  } catch (error) {
    console.error('createUserError:', error)
    if(error.statusCode === notFoundError.statusCode) throw notFoundError
    throw serverError
  }
}

export async function addSpotMember(name: string, email: string) {
  const spotRoleId = 2
  try{
    let userId: string
    let userAuth

    try {
      userAuth = await auth0.getUserByEmail(email)
    } catch (error) {
      userAuth = undefined
    }

    if (userAuth && userAuth.user_id) {
      const user_db = await postgres.query(queries.getUserProfile(), [userAuth.user_id])
      if (user_db.rowCount > 0) {
        userId = user_db.rows[0].user_id
      }
    }

    if (!userId) {
      const newUser = await this.createUser(name, '', email, true)
      userId = newUser.user_id
    }

    if (userId) {
      const roleAdded = await userRolesController.addRole({ user_id: userId, role_id: spotRoleId })
      return {
        userId,
        ...roleAdded,
      }
    }
  } catch (error) {
    console.error('addSpotMember error:', error)
  }
}

/**
 * @param  {CreateUserWithStudent} params
 * @param  {string} params.parent_first_name
 * @param  {string} params.parent_last_name
 * @param  {string} params.phone
 * @param  {string} params.email
 * @param  {string} params.zipcode
 * @param  {string} params.child_first_name
 * @param  {string} params.child_last_name
 * @param  {string} params.grade
 * @param  {string} params.referral_code
 * @param  {string} params.language
 * @param  {string} params.password
 * @returns {Promise} returns an object with the response from the database
 */
export async function createUserWithStudent(params: CreateUserWithStudent): Promise<unknown>{
  await validate(params, schemas.create_user_with_student)
  const {parent_first_name, parent_last_name, phone, email, password} = params
  try {
    const user = await auth0.createUser({
      email,
      password,
      phone,
      name: `${parent_first_name} ${parent_last_name}`,
      is_temp_password: true
    })
    // Get the referral code from the database
    let parent_referral_source = ''
    let user_referred
    const db_response = await postgres.query(queries.getReferralCode(), [params.referral_code])
    // Validate referral code exists
    if(db_response.rowCount > 0) {
      user_referred = db_response.rows[0]
      // Get the user
      const auth0_user = await auth0.getUser(user_referred.user_id)
      // Validate the user exists in the authentication provider
      if (auth0_user.email) {
        // get referral source
        const referral = await SalesforceMethods.getSObjects('Contact', 'id', {
          Email__c: auth0_user.email,
          RecordTypeId: await SalesforceMethods.getRecordTypeParent()
        })
        if (referral.length > 0) {
          parent_referral_source = referral[0].Id
        }
      }
    }
    // Insert or update user profile
    await createUpdateUserProfile({
      user_id: user.user_id,
      email,
      phone,
      first_name: parent_first_name,
      last_name: parent_last_name,
      referred_by: user_referred ? user_referred.user_id : ''
    })
    // Call the creation for parent student
    await sf_controller.creationParentStudentsHandler({
      school_id: 0,
      parent_relationship: 'guardian',
      preferred_contact: 'either',
      email,
      phone,
      zipcode: params.zipcode,
      language: params.language,
      parent_first_name: params.parent_first_name,
      parent_last_name: params.parent_last_name,
      parent_referral_source,
      child_first_name: params.child_first_name,
      child_last_name: params.child_last_name,
      grade: params.grade,
      source: 'Referral',
    })
    return user.user_id
  } catch (error) {
    console.error('createUserWithStudentError:', error)
    throw serverError
  }
}

/**
 * @param  {string} user_id
 * @param  {string} name
 * @param  {string} notification_options
 * @param  {string} search
 * @returns {Promise} returns an object with the response from the database
 */
export async function addSearch(user_id: string, name: string, notification_options: string, search: string): Promise<unknown>{
  const user = await this.getUserDetails(user_id)
  if (!user) throw notFoundError
  try {
    // Add the search and return the result from it
    const now = moment.utc()
    const db_response = await postgres.query(
      insertQuery('searches', {name, user_id, notification_options, search, created_at: now, updated_at: now}),
      [name, user_id, notification_options, search, now, now])
    return  db_response.rows[0]
  } catch (error) {
    console.error('addSearchError:', error)
    if(error.statusCode === notFoundError.statusCode) throw notFoundError
    throw serverError
  }
}

/**
 * @param  {number} search_id
 * @param  {string} user_id
 * @param  {string} name
 * @param  {string} notification_options
 * @param  {string} search
 * @returns {Promise} returns an object with the response from the database
 */
export async function updateSearch(search_id: number, user_id: string, name: string, notification_options: string, search: string): Promise<unknown>{
  const user = await this.getUserDetails(user_id)
  if (!user) throw notFoundError
  try {
    // Validate the search record exists
    let db_response = await postgres.query(queries.getSearch(), [search_id])
    if(db_response.rowCount === 0) throw notFoundError
    // Update the search and return the result from it
    const condition = {id: search_id}
    const built_update = buildUpdate('searches', condition, {
      user_id,
      name,
      notification_options,
      search
    })
    db_response = await postgres.query(built_update.text, built_update.values)
    return db_response.rows[0]
  } catch (error) {
    console.error('updateSearchError:', error)
    if(error.statusCode === notFoundError.statusCode) throw notFoundError
    throw serverError
  }
}

/**
 * @param  {number} search_id
 * @param  {string} user_id
 * @returns {Promise} returns an object with the response from the database
 */
export async function deleteSearch(search_id: number, user_id: string): Promise<unknown>{
  const user = await this.getUserDetails(user_id)
  if (!user) throw notFoundError
  try {
    // Validate the search record exists
    let db_response = await postgres.query(queries.getSearch(), [search_id])
    if(db_response.rowCount === 0) throw notFoundError
    // Deletes the search and return the result from it
    db_response = await postgres.query(queries.deleteSearch(), [search_id, user_id])
    return db_response.rows[0]
  } catch (error) {
    console.error('deleteSearchError:', error)
    if(error.statusCode === notFoundError.statusCode) throw notFoundError
    throw serverError
  }
}

/**
 * @param  {UserInfo} params
 * @param  {string} [params.password]
 * @param  {string} [params.password_new]
 * @param  {string} params.decoded_email
 * @param  {string} params.token
 * @returns {Promise}
 */
export async function updateUserInfo(params: UserInfo): Promise<any>{
  await validate(params, schemas.update_user_info)
  const { password, password_new, decoded_email, token, user_id, ...update_params } = params
  const user = await this.getUserDetails(user_id)
  if (!user) throw notFoundError
  // Validate if the request is to change the user email to a new one
  if (update_params?.email !== user.email) {
    if(!password) {
      console.error('updateUserInfoError: Current password is required to change user email')
      // throw new BadRequestError('Current password is required to change user email')
      return {
        error: 'Your current password is required to change your email'
      }
    }
    // Validate that the provided email is not in use
    const user_id_by_email = await this.getUserIdByEmail(update_params.email)
    if (user_id_by_email.userId) {
      console.error(`updateUserInfoError: Email ${update_params.email} is currently in use by another user`)
      // throw new BadRequestError(`Email ${update_params.email} is currently in use by another user`)
      return {
        error: `The email ${update_params.email} is currently used by other user, please introduce a new one`
      }
    }
  }
  try {
    // Update the user info
    if(update_params.preferred_contact === '') delete update_params.preferred_contact
    await createUpdateUserProfile({
      ...update_params,
      user_id
    })
    const new_user_data = {
      name: `${update_params.first_name} ${update_params.last_name}`,
      email: update_params.email,
    }
    let new_token = ''
    // Update the user password
    if(password && password_new) {
      // Falta obtener el email del token aparentemente. El decoded email
      await auth0.signIn(decoded_email, password)
      // Update the email first
      await auth0.updateUser(user_id, new_user_data)
      // On Auth0 api it's not possible change email and password at same request)
      await auth0.updateUser(user_id, {password: password_new})
      new_token = await this.getNewToken(token)
    } else {
      // Update the user email
      await auth0.updateUser(user_id, new_user_data)
      new_token = await this.getNewToken(token)
    }
    // Update salesforce contact
    if (new_token) {
      const contact_info: SObjectContact = {
        Email: update_params.email,
        FirstName: update_params.first_name,
        LastName: update_params.last_name,
      }
      if (update_params.phone) contact_info.Phone = update_params.phone
      if (update_params.title) contact_info.Title = update_params.title
      await sf_controller.updateContactInfo(contact_info)
    }
    return new_token
  } catch (error) {
    console.error('updateUserInfoError:', error)
    throw serverError
  }
}

/**
 * @param  {string} user_id
 * @param  {string} password
 * @param  {string} token
 * @returns {Promise} returns an object with the response from the database
 */
export async function updatePassword(user_id: string, password: string, token: string): Promise<unknown>{
  const user = await this.getUserDetails(user_id)
  if (!user) throw notFoundError
  try {
    await auth0.updateUser(user_id, { password })
    const new_token = await this.getNewToken(token)
    return {
      idToken: new_token.idToken
    }
  } catch (error) {
    console.error('updatePasswordError:', error)
    if(error.name === 'Bad Request' && error.message.includes('PasswordStrengthError')) throw badRequestError
    throw serverError
  }
}

/**
 * @param  {UserApplication} params
 * @param  {string} params.user_id
 * @param  {string} params.parent_relationship
 * @param  {string} params.parent_first_name
 * @param  {string} params.parent_last_name
 * @param  {string} params.email
 * @param  {string} params.phone
 * @param  {string} params.preferred_contact
 * @param  {Moment} [params.created_at]
 * @param  {number} [params.student_id]
 * @returns {Promise} returns an object with the response from the database
 */
export async function addUserApplication(params: UserApplication): Promise<unknown>{
  await validate(params, schemas.add_user_application)
  const user = await this.getUserDetails(params.user_id)
  if (!user) throw notFoundError
  try {
    params.created_at = moment.utc()
    const values = await getObjectValues(params)
    const db_response = await postgres.query(insertQuery('user_applications', params), values)
    return db_response.rows[0]
  } catch (error) {
    console.error('addUserApplicationError:', error)
    throw serverError
  }
}

/**
 * @param {number} id User application id
 * @param {UserApplication} params
 * @param  {string} params.user_id
 * @param  {string} params.parent_relationship
 * @param  {string} params.parent_first_name
 * @param  {string} params.parent_last_name
 * @param  {string} params.email
 * @param  {string} params.phone
 * @param  {string} params.preferred_contact
 * @param  {Moment} [params.created_at]
 * @param  {number} [params.student_id]
 * @returns {Promise} returns an object with the response from the database
 */
export async function updateUserApplication(id: number, params: UserApplication): Promise<unknown>{
  await validate(params, schemas.add_user_application)
  const user = await this.getUserDetails(params.user_id)
  if (!user) throw notFoundError
  try {
    const built_update = buildUpdate('user_applications', { id }, params)
    const db_response = await postgres.query(built_update.text, built_update.values)
    return db_response.rows[0]
  } catch (error) {
    console.error('updateUserApplicationError:', error)
    throw serverError
  }
}

/**
 * @param {number} id User application id
 * @param {string} user_id User id
 * @returns {Promise} returns an object with the response from the database
 */
export async function deleteUserApplication(id: number, user_id: string): Promise<unknown>{
  const user = await this.getUserDetails(user_id)
  if (!user) throw notFoundError
  try {
    const db_response = await postgres.query(queries.deleteUserApplication(), [id, user_id])
    return db_response.rows[0]
  } catch (error) {
    console.error('deleteUserApplicationError:', error)
    throw serverError
  }
}

/**
 * @param {number} school_id School id
 * @param {string} user_id User id
 * @param {UpdateSchooluser} payload
 * @param {string} updated_by user
 * @returns {Promise} returns an object with the response from the database
 */
export async function updateSchoolUser(school_id: number, user_id: string, payload: UpdateSchooluser, updated_by: string): Promise<unknown>{
  await validate(payload, schemas.update_school_user)
  const client = await transactional_client.connect()
  const now = moment.utc()
  try {
    // Validate the user school record exists
    const db_response = await postgres.query(user_schools_queries.getUserSchoolBySchoolIdAndUserId(), [school_id, user_id])
    // Throw not found error
    if (db_response.rowCount === 0) throw notFoundError
    // Update the user school
    await client.query('BEGIN')
    const update_payload = (payload.isOwner) ? { '"isOwner"': payload.isOwner } : payload
    update_payload.updated_at = now

    const built_update = buildUpdate('users_schools', { school_id, user_id }, update_payload)
    await client.query(built_update.text, built_update.values)
    if (payload.isOwner) {
      await client.query(user_schools_queries.updateSchoolOwners(), [school_id, user_id])
    }
    // Insert log change is not part of the transaction
    await insertLogChange({
      table_name: 'users_schools',
      user_id: updated_by,
      school_id,
      operation_type: 'edit',
      new_values: payload,
      old_values: db_response.rows[0],
      created_at: now
    })

    await client.query('COMMIT')
    return { ...payload, updated_at: now }
  } catch (error) {
    await client.query('ROLLBACK')
    console.error('updateSchoolUserError:', error)
    if(error.statusCode === notFoundError.statusCode) throw notFoundError
    throw serverError
  } finally {
    client.release()
  }
}

/**
 * @param {string} email User email
 * @returns {Promise} returns an object with the response from the database
 */
export async function resetPassword(email: string): Promise<unknown>{
  await validate({email}, schemas.validate_email)
  try {
    const user = await auth0.getUserByEmail(email)
    if(!user) throw notFoundError
    await auth0.sendResetPassword(email)
    return true
  } catch (error) {
    console.error('resetPasswordError:', error)
    if (error.statusCode === notFoundError.statusCode) throw notFoundError
    throw serverError
  }
}

/**
 * @param {SchoolSearchNoMatch} params
 * @param {string} params.name
 * @param {string} [params.student]
 * @param {string} params.email
 * @param {string} params.phone
 * @param {string} [params.zipcode]
 * @param {string} [params.grade]
 * @param {string} [params.year]
 * @param {string} [params.search_url]
 * @returns {Promise} returns true if no error arises
 */
export async function handleSchoolSearchNoMatch(params: SchoolSearchNoMatch): Promise<unknown>{
  await validate(params, schemas.school_search_no_match)
  try {
    // Get and fill template
    const html = schoolSearchNoMatch(params)
    const email_payload = {
      html,
      subject: `${params.name} needs help to find a school!`,
      to: INFO_RECIPIENT,
    }
    await sendEmail(email_payload)
    return true
  } catch (error) {
    console.error('handleSchoolSearchNoMatchError:', error)
    throw serverError
  }
}

/**
 * @param {string} user_id
 * @param {string} name
 * @param {string} phone
 * @param {string} email
 * @returns {Promise} returns the created partner user object
 */
export async function addPartnerUser(user_id: string, name: string, phone: string, email: string): Promise<unknown>{
  await validate({email}, schemas.validate_email)
  try {
    // Get the user profile
    let db_response = await postgres.query(queries.getUserProfile(), [user_id])
    if(db_response.rowCount === 0) throw notFoundError
    const user = db_response.rows[0]
    if(!user.partner_id) throw notFoundError
    const new_user = await this.createUser(name, phone, email)
    db_response = await postgres.query(insertQuery('partner_users', {
      partner_id: user.partner_id,
      user_id: new_user.user_id
    }), [ user.partner_id, new_user.user_id ])
    return db_response.rows[0]
  } catch (error) {
    console.error('addPartnerUserError:', error)
    if (error.statusCode === notFoundError.statusCode) throw notFoundError
    throw serverError
  }
}

/**
 * @param {string} email
 * @returns {Promise} returns a formatted object with the email validation result
 */
export async function validateEmail(email: string): Promise<unknown>{
  await validate({email}, schemas.validate_email)
  return {
    valid: await mailgunValidateEmail(email)
  }
}

/**
 * @param {number} role_id
 * @returns {Promise} returns
 */
export async function getUsersAssignmentsByRoleId(role_id: number): Promise<unknown>{
  try {
    const db_response = await postgres.query(queries.getUsersAssignmentsByRoleId(), [role_id])
    if (db_response.rowCount === 0) throw notFoundError
    return db_response.rows
  } catch (error) {
    console.error('getUsersAssignmentsByRoleIdError:', error)
    if (error.statusCode === notFoundError.statusCode) throw notFoundError
    throw serverError
  }
}

/**
 *
 * @param {Array<string>} user_ids
 * @returns {Promise<Array<object>>} response array with the users information for the given user_ids
 */
export async function getUsersDetails(user_ids: Array<string>) {
  try {
    const users = await auth0.getUsers(user_ids)
    return users
  } catch (error) {
    console.error('getUserDetailsError:', error)
    if(error.statusCode === notFoundError.statusCode) throw notFoundError
    throw serverError
  }
}
/**
 * Gets users by role id
 * @param {number} role_id
 * @returns {Promise<GetUsersByRoleIdResponse>} response an object of GetUsersByRoleIdResponse
 */
export async function getUsersByRoleId(role_id: number): Promise<GetUsersByRoleIdResponse> {
  try {
    const db_response = await postgres.query(queries.getUsersByRoleId(), [role_id])
    return db_response.rows as GetUsersByRoleIdResponse
  } catch (error) {
    console.error('getUsersByRoleId:', error)
    if (error.statusCode === notFoundError.statusCode) throw notFoundError
    throw serverError
  }
}


export async function getLastUserLogin({userId}: {userId: string}): Promise<UserLogAuth> {
  if(!userId) {
    throw badRequestError
  }

  try {
    const logAuthsByUser = await postgres.query(queries.getUserLogins(), [userId])

    const penultimateLogin = getPenultimateLogin(logAuthsByUser.rows)

    return penultimateLogin


  } catch (error) {
    console.log('auth logs error:', error.message)
    throw serverError
  }
}
function getPenultimateLogin(logsByUser: UserLogAuth[]): UserLogAuth | null {
  if(logsByUser.length === 0) return null

  if(logsByUser.length === 1) return logsByUser[0]

  if(logsByUser.length > 2) return logsByUser[1]
}

/**
 * Update the status field for a user profile
 * @param {string} user_id
 * @param {string} status
 * @returns {Promise} updated user profile
 */
export async function updateUserStatus(user_id: string, status: string): Promise<unknown> {
  const user = await getUserDetails(user_id)
  if (!user) throw notFoundError
  try {
    const result = await createUpdateUserProfile({ user_id, status, email: user.email })
    return result
  } catch (error) {
    console.error('updateUserStatusError:', error)
    throw serverError
  }
}

/**
 * Get the spot users
 * @returns {Promise} spot users
 */
export async function getSpotUsers(): Promise<unknown> {
  try {
    const resultAuth0 = await auth0.getSpotUsers()
    const userIds = resultAuth0.map((user: any) => user.user_id)
    const resultDB = await postgres.query(queries.getSpotMembers(), [])
    const userIdsDB = resultDB.rows.map((user: any) => user.user_id)
    const users = [...new Set([...userIds, ...userIdsDB])]
    const spotUsers = await postgres.query(queries.getSpotUsers(), [users])
    return spotUsers.rows
  } catch (error) {
    console.error('getSpotUsersError:', error)
    throw serverError
  }
}

/**
 * Delete the spot users
 * @returns {Promise} spot users
 */
export async function deleteSpotUsers(user_id: string): Promise<unknown> {
  try {
    const spotUsers = await postgres.query(queries.getSpotUser(), [user_id])
    if(spotUsers.rowCount === 0) throw notFoundError

    if (spotUsers.rows[0].cloudtalk_id) {
      console.log('cloudtalk_id', spotUsers.rows[0].cloudtalk_id)
      await cloudTalkController.deleteAgent(spotUsers.rows[0].cloudtalk_id)
    }

    const user_id_auth0 = spotUsers.rows[0].user_id
    const user_data: any = { blocked: true }
    try {
      await auth0.updateUser(user_id_auth0, user_data)
    } catch (error) {
      // If user not found in Auth0, log and continue
      if (error.statusCode === 404 || error.message?.includes('not found')) {
        console.warn(`Auth0 user not found for ${user_id_auth0}, skipping block step.`)
      } else {
        throw error // Only throw for other errors
      }
    }

    const deletedUser = await postgres.query(queries.deleteSpotUser(), [user_id])
    return deletedUser.rows[0]
  } catch (error) {
    console.error('deleteSpotUsersError:', error)
    throw serverError
  }
}

/**
 * @param  {string} user_id
 * @returns {Promise} returns an object with the response from the database
 */
export async function reactivateSpotUser(user_id: string): Promise<unknown> {
  try {
    const user_profiles = await postgres.query(queries.getDeletedSpotUser(), [user_id])
    if(user_profiles.rowCount === 0) throw notFoundError

    const user_profile = user_profiles.rows[0]

    const user_id_auth0 = user_profiles.rows[0].user_id
    const user_data: any = { blocked: false }
    await auth0.updateUser(user_id_auth0, user_data)

    const cloudtalk_agent = await cloudTalkController.addAgent({
      firstname: user_profile.first_name,
      lastname: user_profile.last_name,
      email: user_profile.email,
      pass: generators.generatePassword()
    }, user_profile.user_id)
    const updated_user = await postgres.query(queries.udpdateCloudtalkUser(), [cloudtalk_agent.data.id, user_profile.user_id])

    return updated_user.rows[0]
  } catch (error) {
    console.error('reactivateUserError:', error)
    if (error.statusCode === notFoundError.statusCode) throw notFoundError
    throw serverError
  }
}

/**
 * @param  {string} user_id
 * @returns {Promise} returns the object
 */
export async function removeSuperAdminRole(user_id: string): Promise<unknown> {
  try {
    const result = await auth0.removeUserRole(user_id, 'superadmin')
    return result
  } catch (error) {
    console.error('removeSuperAdminRoleError:', error)
    // If error is user not found in Auth0, return a custom result
    if (error.statusCode === 404 || error.message?.includes('not found')) {
      return { error: 'User not found in Auth0, role removal skipped.' }
    }
    // For other errors, throw
    throw serverError
  }
}


/**
 * @param  {string} user_id
 * @param  {string} cloudtalk_id
 * @returns {Promise} returns the object
 */
export async function addCloudtalkId(user_id: string, cloudtalk_id: string): Promise<unknown> {
  try {
    const result = await postgres.query(queries.addCloudtalkId(), [user_id, cloudtalk_id])
    return result.rows[0]
  } catch (error) {
    console.error('addCloudtalkIdError:', error)
    throw serverError
  }
}
