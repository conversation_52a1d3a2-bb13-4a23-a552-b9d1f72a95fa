// --- Modal Title ---
.modalTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

// --- Tags Section ---
.selectedTags {
  margin-bottom: 8px;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.tag {
  background-color: #5ba3f6;
  color: white;
  font-size: 14px;
  padding: 4px 12px;
  border-radius: 20px;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.tagCloseButton {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 16px;
  line-height: 1;
  padding: 0;

  &:hover {
    opacity: 0.8;
  }
}

.selectNative {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  background-color: white;

  &:focus {
    border-color: #3b82f6;
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
}

// --- Links Section ---
.linksInputContainer {
  display: flex;
  gap: 8px;
}

.linkAddButton {
  min-width: 40px;
}

.linksList {
  margin-top: 12px;
}

.linkItem {
  display: flex;
  align-items: center;
  background: #fafbfc;
  border-radius: 12px;
  padding: 8px 16px;
  margin-bottom: 8px;
  gap: 8px;
}

.linkItemIcon {
  color: #006b8f;
  font-size: 18px;
}

.linkItemAnchor {
  color: #006b8f;
  font-weight: 500;
  text-decoration: none;
  flex: 1;
}

.removeLinkButton {
  color: #222;
  font-size: 16px;
}
/* @import '../../../../../../sass/color-palette'; */

.briefModal {
  :global(.ant-modal-content) {
    border-radius: 0.75rem;
    overflow: hidden;
  }

  :global(.ant-modal-header) {
    padding: 1.5rem 1.5rem 1rem 1.5rem;
    border-bottom: 1px solid #f0f0f0;

    :global(.ant-modal-title) {
      font-size: 1.25rem;
      font-weight: 600;
      color: #1f2937;
    }
  }

  :global(.ant-modal-body) {
    padding: 0 1.5rem 1rem 1.5rem;
    max-height: 70vh;
    overflow-y: auto;
  }

  :global(.ant-modal-footer) {
    padding: 1rem 1.5rem 1.5rem 1.5rem;
    border-top: 1px solid #f0f0f0;
    text-align: right;
  }
}
.modalTitle {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: #111827;
}

.modalContent {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.subtitle {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.required {
  color: #ef4444;
}

.helpIcon {
  color: #9ca3af;
  font-size: 0.75rem;
  cursor: help;

  &:hover {
    color: #6b7280;
  }
}

.input {
  border-radius: 0.5rem;
  border: 1px solid #d1d5db;

  &:focus,
  &:focus-within {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  :global(.ant-input) {
    border: none;
    box-shadow: none;

    &:focus {
      border: none;
      box-shadow: none;
    }
  }
}

.toolbar {
  display: flex;
  gap: 0.25rem;
  padding: 0.5rem;
  background-color: #f9fafb;
  border: 1px solid #d1d5db;
  border-bottom: none;
  border-radius: 0.5rem 0.5rem 0 0;
}

.toolbarButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 0.25rem;
  color: #6b7280;

  &:hover {
    background-color: #e5e7eb;
    color: #374151;
  }
}

.textArea {
  border-radius: 0 0 0.5rem 0.5rem;
  border: 1px solid #d1d5db;
  border-top: none;
  resize: vertical;
  min-height: 8rem;

  &:focus,
  &:focus-within {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  :global(.ant-input) {
    border: none;
    box-shadow: none;

    &:focus {
      border: none;
      box-shadow: none;
    }
  }
}

.select {
  border-radius: 0.5rem;

  :global(.ant-select-selector) {
    border-radius: 0.5rem;
    border: 1px solid #d1d5db;

    &:focus,
    &:focus-within {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }

  :global(.ant-select-focused) {
    :global(.ant-select-selector) {
      border-color: #3b82f6 !important;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
    }
  }
}

.optionContent {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.optionIcon {
  font-size: 1rem;
}

.upload {
  :global(.ant-upload-list) {
    margin-top: 0.5rem;
  }
}

.uploadButton {
  border: 1px dashed #d1d5db;
  border-radius: 0.5rem;
  background-color: #fafafa;
  color: #6b7280;

  &:hover {
    border-color: #3b82f6;
    color: #3b82f6;
    background-color: #f8faff;
  }
}

.linkIcon {
  color: #9ca3af;
}

.advancedSection {
  border-top: 1px solid #f0f0f0;
}

.advancedToggle {
  color: #6b7280;
  font-size: 0.875rem;
  padding: 0;
  height: auto;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  &:hover {
    color: #374151;
  }

  :global(.anticon) {
    transition: transform 0.2s ease;
  }
}

.rotated {
  transform: rotate(180deg);
}

.advancedContent {
  margin-top: 1rem;
  padding: 1rem;
  background-color: #f9fafb;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.advancedText {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0;
  font-style: italic;
}

.switchContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 0;
}

.switchLabel {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}
@media (max-width: 768px) {
  .briefModal {
    :global(.ant-modal) {
      margin: 1rem;
      max-width: calc(100vw - 2rem);
    }

    :global(.ant-modal-content) {
      border-radius: 0.5rem;
    }

    :global(.ant-modal-header),
    :global(.ant-modal-body),
    :global(.ant-modal-footer) {
      padding-left: 1rem;
      padding-right: 1rem;
    }
  }

  .toolbar {
    flex-wrap: wrap;
  }

  .toolbarButton {
    width: 1.75rem;
    height: 1.75rem;
  }
}
