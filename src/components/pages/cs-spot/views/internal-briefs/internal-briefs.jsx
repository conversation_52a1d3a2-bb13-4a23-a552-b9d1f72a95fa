import React, { useState } from 'react';
import { Flex, Button } from 'antd';
import { CheckCircleOutlined, ClockCircleOutlined, WarningOutlined, CalendarOutlined } from '@ant-design/icons';
import { Card } from 'components/card/card';
import ModalBrief from './components/modal-brief';
import styles from './internal-briefs.module.scss';

export const InternalBriefs = () => {
  const [isCreateBriefModalOpen, setIsCreateBriefModalOpen] = useState(false);

  const handleOpenCreateBriefModal = () => {
    setIsCreateBriefModalOpen(true);
  };

  const handleCloseCreateBriefModal = () => {
    setIsCreateBriefModalOpen(false);
  };

  const handleCreateBrief = (briefData) => {
    console.log('Creating brief:', briefData);
    // Here you would typically send the data to your API
    // For now, we'll just log it and close the modal
  };

  return (
    <>
      <div className={styles.cardsRow} style={{ width: '100%' }}>
        <Card className={`${styles.cardItem} spacing-py-8 spacing-px-16 box-shadow-hover`}>
          <Flex align="center" gap={16} className={styles.cardContent}>
            <div className={styles.iconBlueContainer}>
              <CheckCircleOutlined className={`${styles.cardIcon} ${styles.iconBlue}`} />
            </div>
            <div>
              <div className={styles.cardNumber}>9</div>
              <div className={styles.cardTitle}>Total Briefs</div>
              <div className={styles.cardSubtitle}>All active briefs</div>
            </div>
          </Flex>
        </Card>
        <Card className={`${styles.cardItem} spacing-py-8 spacing-px-16 box-shadow-hover`}>
          <Flex align="center" gap={16} className={styles.cardContent}>
            <div className={styles.iconGreenContainer}>
              <ClockCircleOutlined className={`${styles.cardIcon} ${styles.iconGreen}`} />
            </div>
            <div>
              <div className={styles.cardNumber}>0</div>
              <div className={styles.cardTitle}>New Today</div>
              <div className={styles.cardSubtitle}>Created in last 24hrs</div>
            </div>
          </Flex>
        </Card>
        <Card className={`${styles.cardItem} spacing-py-8 spacing-px-16 box-shadow-hover`}>
          <Flex align="center" gap={16} className={styles.cardContent}>
            <div className={styles.iconYellowContainer}>
              <WarningOutlined className={`${styles.cardIcon} ${styles.iconYellow}`} />
            </div>
            <div>
              <div className={styles.cardNumber}>4</div>
              <div className={styles.cardTitle}>Urgent</div>
              <div className={styles.cardSubtitle}>Expiring within 7 days</div>
            </div>
          </Flex>
        </Card>
        <Card className={`${styles.cardItem} spacing-py-8 spacing-px-16 box-shadow-hover`}>
          <Flex align="center" gap={16} className={styles.cardContent}>
            <div className={styles.iconRedContainer}>
              <CalendarOutlined className={`${styles.cardIcon} ${styles.iconRed}`} />
            </div>
            <div>
              <div className={styles.cardNumber}>4</div>
              <div className={styles.cardTitle}>Expiring Soon</div>
              <div className={styles.cardSubtitle}>Expiring within 3 days</div>
            </div>
          </Flex>
        </Card>
      </div>
      <div className={styles.briefsSection}>
        <div className={styles.briefsSearchBar}>
          <Flex gap={16} align="center" className={styles.briefsSearchBar}>
            <input
              className={styles.searchInput}
              type="text"
              placeholder="Search briefs, tags, content, or try natural language..."
            />
            <Flex gap={12} className={styles.searchActions}>
              <Button color="default" variant="outlined">
                <CheckCircleOutlined style={{ fontSize: 18, color: '#2b6ef7' }} />
                All Types
              </Button>
              <Button color="default" variant="outlined">
                <CalendarOutlined style={{ fontSize: 18, color: '#2b6ef7' }} />
                Last 60 days
              </Button>
              <Button type="primary" onClick={handleOpenCreateBriefModal}>
                + New Brief
              </Button>
            </Flex>
          </Flex>
        </div>
        <Flex justify="center" vertical gap={16} className={styles.briefsEmptyState}>
          <div className={styles.emptyText}>No briefs match your filters</div>
          <div>
            <Button type="primary" className={styles.createBriefBtn} onClick={handleOpenCreateBriefModal}>
              + Create First Brief
            </Button>
          </div>
        </Flex>
      </div>

      <ModalBrief open={isCreateBriefModalOpen} onClose={handleCloseCreateBriefModal} onSubmit={handleCreateBrief} />
    </>
  );
};
